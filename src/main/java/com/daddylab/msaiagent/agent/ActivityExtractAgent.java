package com.daddylab.msaiagent.agent;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
import com.alibaba.dashscope.common.MultiModalMessage;
import com.alibaba.dashscope.common.Role;
import com.daddylab.msaiagent.common.base.exception.AiAgentErrorCodeEnum;
import com.daddylab.msaiagent.common.base.exception.AiAgentException;
import com.daddylab.msaiagent.common.oss.OssGateway;
import com.daddylab.msaiagent.config.LlmConfig;
import com.daddylab.msaiagent.db.aiAgent.entity.ActivityMaterial;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.content.Media;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.openai.api.ResponseFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.MimeTypeUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 活动素材解析Agent
 */
@Slf4j
@Component
public class ActivityExtractAgent {

    @Value("classpath:md/ActivityExtract.md")
    private Resource systemPrompt;
    @Value("${bailian.appApiKey}")
    private String qWenKey;
    @Autowired LlmConfig llmConfig;
    @Autowired
    private OssGateway ossGateway;

    /**
     * 获取系统提示词
     */
    private String getSystemPrompt() {
        try {
            byte[] contentAsByteArray = systemPrompt.getContentAsByteArray();
            String template = new String(contentAsByteArray);
            Map<String, Object> params = new HashMap<>();
            params.put("date", DateUtil.format(new Date(), "yyyy年MM月dd日"));
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                template = StrUtil.replace(template, "{" + entry.getKey() + "}", String.valueOf(entry.getValue()));
            }
            return template;
        } catch (Exception e) {
            log.error("[模版渲染] 失败", e);
        }
        return StrUtil.EMPTY;
    }

    /**
     * 处理多模态文件(gemini)
     */
    private List<Media> processMedia(String originalFileUrl){
        String[] urls = originalFileUrl.split(",");
        List<Media> mediaList = new java.util.ArrayList<>(urls.length > 0 ? List.of() : null);
        for (String url : urls) {
            if (StrUtil.isNotBlank(url)) {
                try {
                    String lowerUrl = url.toLowerCase();
                    String ossUrl = ossGateway.generatePresignedUrl(url);
                    String mimeType;
                    if (lowerUrl.endsWith(".png")) {
                        mimeType = "image/png";
                    } else if (lowerUrl.endsWith(".jpg") || lowerUrl.endsWith(".jpeg")) {
                        mimeType = "image/jpeg";
                    } else {
                        mimeType = "application/octet-stream";
                    }
                    // 下载图片数据
                    byte[] imageData = downloadImage(ossUrl);
                    if (imageData != null) {
                        Resource resource = new ByteArrayResource(imageData);
                        Media media = new Media(MimeTypeUtils.parseMimeType(mimeType), resource);
                        mediaList.add(media);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("处理多模态文件失败: {}", url);
                }
            }
        }
        return mediaList;
    }

    /**
     * 处理多模态文件(qwen)
     */
    private List<String> processMediaQwen(String originalFileUrl) {
        String[] urls = originalFileUrl.split(",");
        List<String> res = new ArrayList<>();
        for (String url : urls) {
            if (StrUtil.isNotBlank(url)) {
                String ossUrl = ossGateway.generatePresignedUrl(url);
                Map<String, String> imgInfo = ossGateway.imageInfo(ossUrl);
                log.info("图片信息:{}",imgInfo.toString());
                List<String> corpList= imageCropper(ossUrl,imgInfo.get("ImageWidth") == null ? 1024 : Integer.parseInt(imgInfo.get("ImageWidth")),
                        imgInfo.get("ImageHeight") == null ? 1024 : Integer.parseInt(imgInfo.get("ImageHeight")));
                if(corpList.isEmpty()){
                    res.add(ossUrl);
                }else{
                    res.addAll(corpList);
                }
            }
        }
        return res;
    }
    private static final int PIXELS_PER_TOKEN = 28;
    private static final int MAX_TOKENS = 16384;
    /**
     * 裁剪图片
     */
    private List<String> imageCropper(String url, int width, int height) {
        List<String> res = new ArrayList<>();
        // 1. 计算最大允许高度
        int widthTokens = (int) Math.ceil((double) width / PIXELS_PER_TOKEN);
        int maxHeightTokens = MAX_TOKENS / widthTokens;
        int maxHeightPixels = maxHeightTokens * PIXELS_PER_TOKEN;
        // 2. 计算需要裁剪的段数
        int segments = (int) Math.ceil((double) height / maxHeightPixels);
        System.out.printf("将 %dx%d 图片切割为 %d 段 (每段高度≤%dpx)%n",
                width, height, segments, maxHeightPixels);
        if (segments <= 1) {// 如果不需要裁剪，直接返回原图
            return res;
        }
        // 3. 读取原始并裁剪图片
        try {
            BufferedImage original = ImageIO.read(new URL(url));
            for (int i = 0; i < segments; i++) {
                int startY = i * maxHeightPixels;
                int segmentHeight = Math.min(maxHeightPixels, height - startY);
                // 创建分段图片
                BufferedImage segment = new BufferedImage(width, segmentHeight, BufferedImage.TYPE_INT_RGB);
                segment.getGraphics().drawImage(
                        original,
                        0, 0, width, segmentHeight,     // 目标区域
                        0, startY, width, startY + segmentHeight, // 源区域
                        null
                );
                // 保存分段
                String outputPath = "output" + (i + 1) + ".jpg";
                ImageIO.write(segment, "jpg", new File(outputPath));
                FileInputStream fis = new FileInputStream(outputPath);
                // 上传到OSS
                String ossPath = String.format("/image/%s/%s.jpg",
                        DatePattern.PURE_DATE_FORMAT.format(new Date()),
                        DateUtil.format(new DateTime(), "HHmmssSSS"));
                String ossUrl = ossGateway.put(ossPath, fis);
                System.out.printf("保存分段 %d: %dx%d -> %s%n",
                        i + 1, width, segmentHeight, outputPath);
                res.add(ossGateway.generatePresignedUrl(ossUrl));
            }
        }catch (Exception e){
            log.error("处理图片失败: {}", e.getMessage());
            throw new AiAgentException(AiAgentErrorCodeEnum.BUSINESS_ERROR,"处理图片失败");
        }
        return res;
    }

    /**
     * 下载图片数据
     */
    private byte[] downloadImage(String url) {
        try {
            URI uri = URI.create(url);
            URL imageUrl = uri.toURL();
            try (InputStream inputStream = imageUrl.openStream()) {
                return inputStream.readAllBytes();
            }
        } catch (Exception e) {
            log.error("下载图片失败: {}", url, e);
            return null;
        }
    }

    /**
     * 调用AI模型进行活动素材解析
     */
    public String chatComplete(ActivityMaterial material) {
        String sysPrompt = getSystemPrompt();
        List<Media> media = processMedia(material.getOriginalFileUrl());
        LlmConfig.LLMConfigDto config = llmConfig.getConfigs().get("google-gemini-2_5-pro-preview");
        OpenAiApi openAiConfig =
                OpenAiApi.builder()
                        .apiKey(config.getApiKey())
                        .baseUrl(config.getBaseUrl())
                        .completionsPath(config.getCompletionsPath())
                        .build();
        // 构造多模态UserMessage
        UserMessage userMessage = UserMessage.builder().text(sysPrompt).media(media).build();
        Prompt prompt = new Prompt(userMessage);

        final OpenAiChatModel chatModel =
                OpenAiChatModel.builder()
                        .defaultOptions(
                                OpenAiChatOptions.builder()
                                        .model(config.getModelName())
                                        .temperature(1.0)
                                        .topP(0.95)
                                        .responseFormat(
                                                ResponseFormat.builder().type(ResponseFormat.Type.JSON_OBJECT).build())
                                        .build())
                        .openAiApi(openAiConfig)
                        .build();
        //return chatModel.call(prompt).getResult().getOutput().getText();
        // 流式调用
        StringBuilder result = new StringBuilder();
        try {
            chatModel.stream(prompt).blockLast();
            Flowable.fromPublisher(chatModel.stream(prompt))
                    .blockingForEach(response -> {
                        String text = response.getResult().getOutput().getText();
                        if (StrUtil.isNotBlank(text)) {
                            result.append(text);
                        }
                    });
        } catch (Exception e) {
            log.error("流式调用失败: {}", e.getMessage());
            return StrUtil.EMPTY;
        }
        return result.toString();
    }

    public String chatQWen(ActivityMaterial material){
        String sysPrompt = getSystemPrompt();
        List<String> media = processMediaQwen(material.getOriginalFileUrl());
        List<Map<String, Object>> userContent = new java.util.ArrayList<>();
        for (String url : media) {
            userContent.add(Collections.singletonMap("image", url));
        }
        userContent.add(Collections.singletonMap("text", "这是小红书活动信息的图片，提取信息，输出JSON，不要带```json头。"));
        MultiModalConversation conv = new MultiModalConversation();
        MultiModalMessage systemMessage = MultiModalMessage.builder().role(Role.SYSTEM.getValue())
                .content(List.of(Collections.singletonMap("text", sysPrompt))).build();
        MultiModalMessage userMessage = MultiModalMessage.builder().role(Role.USER.getValue())
                .content(userContent).build();
        MultiModalConversationParam param = MultiModalConversationParam.builder()
                .apiKey(qWenKey)
                .model("qwen-vl-max-latest")  // 此处以qwen-vl-max-latest为例，可按需更换模型名称。模型列表：https://help.aliyun.com/model-studio/getting-started/models
                .messages(Arrays.asList(systemMessage, userMessage))
                .parameter("vl_high_resolution_images", true) // 是否开启高分辨率图像处理
                .build();
        try{
            //MultiModalConversationResult result = conv.call(param);
            Flowable<MultiModalConversationResult> result = conv.streamCall(param);
            AtomicReference<String> res = new AtomicReference<>("");
            result.blockingForEach(item -> {
                String temp= item.getOutput().getChoices().get(0).getMessage().getContent().get(0).get("text").toString();
                if(StrUtil.isNotBlank(temp)){
                    res.set(temp);
                }
                //System.out.println(temp);
            });
            //return result.getOutput().getChoices().get(0).getMessage().getContent().get(0).get("text").toString();
            return res.get();
        }catch (Exception e){
            log.error("调用Qwen模型失败: {}", e.getMessage());
            return StrUtil.EMPTY;
        }
    }


    public void main(String[] args) {
        ActivityExtractAgent agent = new ActivityExtractAgent();
        String res = agent.chatComplete(new ActivityMaterial() {{
            setOriginalFileUrl("https://daddyoss-private-test.oss-cn-hangzhou.aliyuncs.com/母亲节.png");
        }});
        //母亲节活动
        //https://daddyoss-private-test.oss-cn-hangzhou.aliyuncs.com/母亲节.png
        //六一活动
        //https://daddyoss-private-test.oss-cn-hangzhou.aliyuncs.com/一起成长更快乐.pdf
        System.out.println(res);
    }
}
