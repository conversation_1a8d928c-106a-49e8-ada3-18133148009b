package com.daddylab.msaiagent.db.aiAgent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.daddylab.msaiagent.db.base.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <p>
 * AI模型Token使用记录表
 * </p>
 *
 * <AUTHOR> Generator
 * @since 2025-06-17
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
@TableName("ai_token_usage")
public class AiTokenUsage extends Entity<AiTokenUsage> {

    private static final long serialVersionUID = 1L;

    /**
     * 请求唯一标识
     */
    @TableField("request_id")
    private String requestId;

    /**
     * 模型名称
     */
    @TableField("model_name")
    private String modelName;

    /**
     * 模型提供商 (如: openai, dashscope, azure)
     */
    @TableField("model_provider")
    private String modelProvider;

    /**
     * 调用方法名
     */
    @TableField("method_name")
    private String methodName;

    /**
     * 调用类名
     */
    @TableField("class_name")
    private String className;

    /**
     * 输入Token数量
     */
    @TableField("input_tokens")
    private Integer inputTokens;

    /**
     * 输出Token数量
     */
    @TableField("output_tokens")
    private Integer outputTokens;

    /**
     * 总Token数量
     */
    @TableField("total_tokens")
    private Integer totalTokens;

    /**
     * 请求开始时间戳(毫秒)
     */
    @TableField("request_start_time")
    private Long requestStartTime;

    /**
     * 请求结束时间戳(毫秒)
     */
    @TableField("request_end_time")
    private Long requestEndTime;

    /**
     * 请求耗时(毫秒)
     */
    @TableField("duration_ms")
    private Long durationMs;

    /**
     * 是否成功 1-成功 0-失败
     */
    @TableField("is_success")
    private Integer isSuccess;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 预估成本(美元)
     */
    @TableField("estimated_cost")
    private BigDecimal estimatedCost;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 业务类型
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 扩展信息(JSON格式)
     */
    @TableField("extra_info")
    private String extraInfo;
}
