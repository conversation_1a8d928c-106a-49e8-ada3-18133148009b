package com.daddylab.msaiagent.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.msaiagent.config.HttpLoggingConfig;
import com.daddylab.msaiagent.config.LlmConfig;
import com.daddylab.msaiagent.db.aiAgent.dao.ModelChatDao;
import com.daddylab.msaiagent.db.aiAgent.entity.ModelChat;
import com.daddylab.msaiagent.domain.enums.OpenRouterModelEnum;
import com.daddylab.msaiagent.service.ModelChatService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.net.URI;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.*;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.content.Media;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.ClientHttpRequestFactories;
import org.springframework.boot.web.client.ClientHttpRequestFactorySettings;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;

import org.springframework.util.MimeTypeUtils;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ByteArrayResource;

import java.net.URL;
import java.io.InputStream;

@Service
@Slf4j
public class ModelChatServiceImpl implements ModelChatService {

    private ConcurrentHashMap<String, OpenAiChatModel> chatModels;
    private ConcurrentHashMap<String,OpenAiChatModel> proxyChatModels;

    @Autowired
    LlmConfig llmConfig;

    @Autowired
    private ModelChatDao modelChatDao;

    @Override
    public ModelChat createChat(String modelName, String chatType) {
        ModelChat chat = new ModelChat();
        chat.setChatId(RandomUtil.randomStringUpper(4) + "-" + RandomUtil.randomNumbers(6));
        chat.setModelName(modelName);
        chat.setChatType(chatType);
        chat.setStatus(1);
        chat.setContent("");
        modelChatDao.save(chat);
        return chat;
    }

    public List<Message> getChatMessages(ModelChat chat) {
        String content = chat.getContent();
        List<Message> messages = new ArrayList<>();

        if (null != content && !content.isEmpty()) {
            try {
                // 使用自定义逻辑处理Message接口的反序列化
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonArray = objectMapper.readTree(content);

                if (jsonArray.isArray()) {
                    for (JsonNode node : jsonArray) {
                        String type = node.has("type") ? node.get("type").asText() : "";
                        if (type == null || type.isEmpty()) {
                            type = node.has("messageType") ? node.get("messageType").asText() : "";
                            type = type.toLowerCase();
                        }
                        String text = node.has("text") ? node.get("text").asText() : "";
                        // 根据类型创建具体的Message实现 fixme 还有很多参数没有兼容 比如 metadata media
                        if (MessageType.USER.getValue().equals(type)) {
                            messages.add(new UserMessage(text));
                        } else if (MessageType.ASSISTANT.getValue().equals(type)) {
                            messages.add(new AssistantMessage(text));
                        } else if (MessageType.SYSTEM.getValue().equals(type)) {
                            messages.add(new SystemMessage(text));
                        }
                        // 可以根据需要添加更多消息类型的处理
                    }
                }
            } catch (IOException e) {
                log.error("解析聊天记录异常", e);
            }
        }

        return messages;
    }

    @Override
    public void startChatAsync(ModelChat chat, String promptStr, Consumer<String> callback, Consumer<String> errorCallback) {
        startChatAsync(chat, promptStr, null, callback, errorCallback);
    }

    @Override
    public void startChatAsync(ModelChat chat, String promptStr, List<String> images, Consumer<String> callback, Consumer<String> errorCallback) {
        OpenAiChatModel model = getChatModel(chat.getModelName());
        if (null == model) {
            chat.setErrorContent("模型配置不存在");
            modelChatDao.updateById(chat);
            errorCallback.accept(chat.getErrorContent());
            return;
        }
        List<Message> messages = getChatMessages(chat);
        if (null == messages) {
            chat.setErrorContent("解析历史聊天信息异常");
            modelChatDao.updateById(chat);
            errorCallback.accept(chat.getErrorContent());
            return;
        }
        Thread.startVirtualThread(() -> {
            UserMessage userMessage = UserMessage.builder().text(promptStr).media(processMedia(images)).build();
            messages.add(userMessage);
            Prompt prompt = new Prompt(messages);

            StringBuilder fullResponseBuilder = new StringBuilder();

            try {
                log.info("🚀 开始流式聊天，模型: {}, 用户输入: {}", chat.getModelName(), "省略");

                Flux<ChatResponse> responseFlux = model.stream(prompt);

                responseFlux
                    .doOnNext(chatResponse -> {
                        chatResponse.getMetadata().getUsage()
                        // 获取当前流式响应的内容
                        String deltaContent = chatResponse.getResult().getOutput().getText();
                        if (deltaContent != null && !deltaContent.isEmpty()) {
                            // 在控制台实时打印流式输出
                            System.out.print(deltaContent);
                            System.out.flush(); // 确保立即输出

                            // 累积完整的响应内容
                            fullResponseBuilder.append(deltaContent);

                            // 也可以通过callback实时返回片段（可选）
                            // callback.accept(deltaContent);
                        }
                    })
                    .doOnComplete(() -> {
                        System.out.println(); // 换行
                        String fullResponse = fullResponseBuilder.toString();
                        log.info("✅ 流式聊天完成，完整响应长度: {}", fullResponse.length());

                        // 创建完整的助手消息
                        AssistantMessage assistantMessage = new AssistantMessage(fullResponse);
                        messages.add(assistantMessage);

                        // 保存聊天记录
                        chat.setContent(serializeMessages(messages));
                        modelChatDao.updateById(chat);

                        // 通过callback返回完整响应
                        callback.accept(fullResponse);
                    })
                    .doOnError(error -> {
                        System.out.println(); // 换行
                        String errorMsg = "流式聊天异常：" + error.getMessage();
                        log.error("❌ {}", errorMsg, error);

                        chat.setErrorContent(errorMsg);
                        modelChatDao.updateById(chat);
                        errorCallback.accept(errorMsg);
                    })
                    .doOnCancel(() -> {
                        System.out.println(); // 换行
                        String cancelMsg = "流式聊天被取消";
                        log.warn("⚠️ {}", cancelMsg);

                        // 如果有部分响应，也保存起来
                        String partialResponse = fullResponseBuilder.toString();
                        if (!partialResponse.isEmpty()) {
                            AssistantMessage assistantMessage = new AssistantMessage(partialResponse + "\n[响应被中断]");
                            messages.add(assistantMessage);
                            chat.setContent(serializeMessages(messages));
                            modelChatDao.updateById(chat);
                            callback.accept(partialResponse);
                        } else {
                            chat.setErrorContent(cancelMsg);
                            modelChatDao.updateById(chat);
                            errorCallback.accept(cancelMsg);
                        }
                    })
                    .subscribe(); // 启动流式处理

            } catch (Exception e) {
                String errorMsg = "发起流式聊天异常：" + e.getMessage();
                log.error("❌ {}", errorMsg, e);

                chat.setErrorContent(errorMsg);
                modelChatDao.updateById(chat);
                errorCallback.accept(errorMsg);
            }
        });
    }

    // 添加一个辅助方法，用于序列化消息列表
    private String serializeMessages(List<Message> messages) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            List<Object> serializedMessages = new ArrayList<>();

            for (Message message : messages) {
                if (message instanceof UserMessage) {
                    serializedMessages.add(Map.of(
                            "type", "user",
                            "text", message.getText()
                    ));
                } else if (message instanceof AssistantMessage) {
                    serializedMessages.add(Map.of(
                            "type", "assistant",
                            "text", Objects.requireNonNull(message.getText())
                    ));
                }
                // 可以添加更多消息类型的处理
            }

            return objectMapper.writeValueAsString(serializedMessages);
        } catch (Exception e) {
            log.error("序列化消息列表异常", e);
            return "[]";
        }
    }

    public OpenAiChatModel getChatModel(String modelName) {
        OpenRouterModelEnum modelEnum = OpenRouterModelEnum.getEnumByName(modelName);
        if (null == modelEnum) {
            return null;
        }
        if (chatModels == null) {
            chatModels = new ConcurrentHashMap<>();
        }
        if (null == llmConfig.getConfigs() || null == llmConfig.getConfigs().get(modelEnum.getKey())) {
            log.error("Model configuration missing or model not found: {}", modelEnum.getKey());
            return null;
        }
        LlmConfig.LLMConfigDto config = llmConfig.getConfigs().get(modelEnum.getKey());

        return chatModels.computeIfAbsent(modelEnum.getName(), k -> {
            try {
                // 创建自定义的RestClient.Builder并配置超时
                BufferingClientHttpRequestFactory requestFactory =
                        new BufferingClientHttpRequestFactory(
                                ClientHttpRequestFactories.get(ClientHttpRequestFactorySettings.DEFAULTS
                                        .withConnectTimeout(Duration.ofSeconds(60))
                                        .withReadTimeout(Duration.ofSeconds(3600)))
                        );

                RestClient.Builder customBuilder = RestClient.builder()
                        .requestFactory(requestFactory)
                        // 使用专门的curl日志拦截器
                        .requestInterceptor(HttpLoggingConfig.createCurlLoggingInterceptor());


                OpenAiApi openAiConfig = OpenAiApi.builder()
                        .apiKey(config.getApiKey())
                        .baseUrl(config.getBaseUrl())
                        .completionsPath(config.getCompletionsPath())
                        .restClientBuilder(customBuilder)
                        .build();

                return OpenAiChatModel.builder()
                        .defaultOptions(OpenAiChatOptions.builder()
                                .model(modelName)
                                .temperature(1.0)
                                .topP(0.95)
                                .build())
                        .openAiApi(openAiConfig)
                        .build();

            } catch (Exception e) {
                log.error("⚠️ 自定义HTTP客户端配置失败，使用默认配置: {}", e.getMessage());
                // 降级到默认配置
                OpenAiApi openAiConfig = OpenAiApi.builder()
                        .apiKey(config.getApiKey())
                        .baseUrl(config.getBaseUrl())
                        .completionsPath(config.getCompletionsPath())
                        .build();

                return OpenAiChatModel.builder()
                        .defaultOptions(OpenAiChatOptions.builder()
                                .model(modelName)
                                .temperature(1.0)
                                .topP(0.95)
                                .build())
                        .openAiApi(openAiConfig)
                        .build();
            }
        });
    }


    @Override
    public OpenAiChatModel getChatModelProxyEnum(OpenRouterModelEnum modelEnum) {
        if (proxyChatModels == null) {
            proxyChatModels = new ConcurrentHashMap<>();
        }
        if(null == llmConfig.getConfigs() || null == llmConfig.getConfigs().get(modelEnum.getKey())) {
            log.error("Model configuration missing or model not found: {}", modelEnum.getKey());
            return null;
        }
        LlmConfig.LLMConfigDto config = llmConfig.getConfigs().get(modelEnum.getKey());
        OpenAiApi openAiConfig = OpenAiApi.builder()
                .apiKey(config.getApiKey())
                .baseUrl(config.getBaseUrl())
                .completionsPath(config.getCompletionsPath())
                .build();
        return proxyChatModels.computeIfAbsent(modelEnum.getName(), k -> OpenAiChatModel.builder()
                .defaultOptions(OpenAiChatOptions.builder()
                        .model(modelEnum.getName())
                        .temperature(1.0)
                        .topP(0.95)
                        .build())
                .openAiApi(openAiConfig)
                .build());
    }

    @Override
    public ModelChat getChat(String modelName, String chatType) {
        return modelChatDao.lambdaQuery().eq(ModelChat::getModelName, modelName).eq(ModelChat::getChatType, chatType).one();
    }

    @Override
    public ModelChat getChat(Long id) {
        return modelChatDao.getById(id);
    }

    /**
     * 处理多模态文件(gemini)
     */
    private List<Media> processMedia(List<String> images) {
        if (null == images || images.isEmpty()) {
            return new ArrayList<>();
        }
        List<Media> mediaList = new java.util.ArrayList<>(images.size());
        for (String url : images) {
            if (StrUtil.isNotBlank(url)) {
                try {
                    String lowerUrl = url.toLowerCase();
                    String mimeType;
                    if (lowerUrl.endsWith(".png")) {
                        mimeType = "image/png";
                    } else if (lowerUrl.endsWith(".jpg") || lowerUrl.endsWith(".jpeg")) {
                        mimeType = "image/jpeg";
                    } else {
                        mimeType = "application/octet-stream";
                    }
                    // 下载图片数据
                    byte[] imageData = downloadImage(url);
                    if (imageData != null) {
                        Resource resource = new ByteArrayResource(imageData);
                        Media media = new Media(MimeTypeUtils.parseMimeType(mimeType), resource);
                        mediaList.add(media);
                    }
                } catch (Exception e) {
                    log.error("处理多模态文件失败: {}", url, e);
                }
            }
        }
        return mediaList;
    }

    /**
     * 下载图片数据
     */
    private byte[] downloadImage(String url) {
        try {
            URI uri = URI.create(url);
            URL imageUrl = uri.toURL();
            try (InputStream inputStream = imageUrl.openStream()) {
                return inputStream.readAllBytes();
            }
        } catch (Exception e) {
            log.error("下载图片失败: {}", url, e);
            return null;
        }
    }
}
